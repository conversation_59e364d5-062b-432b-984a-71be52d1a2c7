Ai后台-门店管理&账户
1. AI运营后台-Ai外呼充值包
   模块 名称 核心需求 参考图片
   Ai外呼充值包
1. 新增【Ai外呼充值包】 a. Ai外呼充值包界面 i. 添加新套餐 1. 套餐名称：不超过20个字符 2. 价格：大于0，且取整 3. 点数：此点数与Ai外呼进行关联，每成功一个订单，则消耗一个点数 ii. 设置套餐不超过5个 iii. 排序：从点数从低到高排序 iv. 充值包变更： 1. 已购买充值包，按购买套餐内容兑现； 2. 购买中，则页面提示：“充值包正在升级，请10s后刷新再提交”；按最新已保存套餐进行变更 3. 未购买：按最新的来兑现； b. 现有套餐列表 i. 套餐名称 ii. 价格 iii. 点数 操作： i. 支持编辑：编辑后，原有内容覆盖，所有门店执行新充值包标准 ii. 支持删除，删除后，展示删除后的充值包付费
   Ai外呼点数
1. 新增【Ai外呼点数记录】：展示所有门店的Ai外呼点数变动
   ◦ 按门店：支持通过门店名称或Code进行搜索，筛选特定门店的余额明细。
   ◦ 按类型：下拉选择【注册奖励，后台赠送，充值获取、外呼消耗】等交易类型进行筛选。 b. 列表字段：
   ▪ 门店信息：门店Code+门店名称（可点击查看详情）
   ▪ 类型：注册奖励（绿色）、后台赠送（黄色）、充值获取（绿色）
   ▪ 点数变化：+100点、+500点等（绿色为增加，红色为减少）
   ▪ 获取后点数：变更后的总点数（橙色显示）
   ▪ 操作人：系统自动、管理员姓名、用户自助
   ▪ 日期：记录产生时间
   ▪ 详情/备注：详细说明信息
   ▪ 自动生成详情（系统触发） 注册奖励：门店注册成功时，系统自动生成备注格式为"新店注册赠送{点数}点外呼点数"，如"新店注册赠送100点外呼点数"。 充值获取：门店充值成功时，系统自动生成备注格式为"充值{金额}元获得{点数}点外呼点数"，如"充值500元获得1000点外呼点数"。 外呼消耗：AI外呼系统每次拨打电话时，实时生成备注格式为"外呼客户{携程美团等OTA订单号}，消耗1点"，如"外呼客户在“渠道”订单号-携程/美团等OTA同步的订单号，消耗1点"。
2. AI运营后台-新增【Ai工具门店列表】
   模块 名称 核心需求 参考图片
   登陆页 Ai工具端/智能工单APP/运营后台/门店后台，支持登陆页输入账号密码登录 账号：自定义账号/手机号/邮箱
   门店管理 门店管理（圈选部分去掉）
1. 门店管理更名：Ai客服门店管理
2. 列表更新：隐藏“门店邀请码”、“被邀请码”、“邀请人”字段
3. 门店列表-点击【编辑】： a. 去掉【设置到期时间】、【携程EBK链接】 b. 将【OTA智能体】改成【Ai工具】
4. 门店列表-点击【详情】 a. 去掉【设置到期时间】 b. 将【OTA智能体】改成【Ai工具】 c. 去掉【OTA时间变更记录】
   设备管理
1. 设备库存-新增字段 运营后台：设备列表内新增“”App版本号“字段 门店后台：设备列表新增“”App版本号“字段
   • APP版本号：
   ◦ 通过设备状态【绑定】，通过硬件绑定的APP版本号进行同步，如版本更新也会进行同步（客户端会上报版本号）
   ◦ 如设备状态【未绑 运营后台 门店后台
   黄文英 7月28日 17:38 门店列表-点击【编辑】：去掉【设置到… 原页面需删减部分，已更新 黄文英 12分钟前 设备库存-新增字段 运营后台：设备列表… @黄伟杰 新增需求，请知悉 黄文英 2小时前 运营后台：设备列表内新增“”App版本… @范礼兴 @沈汝琪 @汪维 APP版本功能即将新增，补充字段，知悉下下 a. 运营后台-设备管理-APP版本 i. 当渠道选择ios的时候，页面不需改变 1. APP下载地址为非必填 ii. 当渠道选择安卓版本的时候 1. 支持本地文件上传，上传后解析下载地址，非必填
   新增【Ai工具门店列表】
1. 菜单栏展示： Ai后台菜单栏和权限
2. 新增【Ai工具门店列表】 a. 数据来源： i. 同步所有包含Ai工具产品的门店展示 ii. 如Ai工具产品未展示，则不在此处展示 b. 筛选与搜索 *   门店名称：支持通过门店Code或门店名称进行模糊搜索。 *   注册邀请码：支持通过注册邀请码进行精确搜索，快速定位通过特定邀请码注册的门店。 *   标签筛选：提供标签下拉框，允许运营人员按邀请来源标签（例如“渠道A专属”、“夏季特惠”）筛选门店。 *   链接名称搜索：支持通过邀请链接名称进行模糊搜索，定位由特定链接带来的门店。 c. 列表字段展示： *   门店名称+门店Code：门店的唯一标识符。 【Ai工具门店列表】 设置奖励金 设置免费时长 设置外呼点数 门店详情入口： 身用于邀请其他门店加入的邀请码（如果门店支持邀请）。如果门店没有此功能，如无则展示“-”。点击跳转【邀请门店记录】 *   邀请门店数：统计该门店对外邀请并成功创建门店数 *   邀请人信息：邀请人姓名+手机号码。 *   邀请门店：（门店名称+code）+注册邀请码：该门店注册时使用的外部邀请码。空表示非邀请注册。如无则展示“-” *   标签：该门店注册邀请码所属的标签名称。点击标签可查看该标签的详细内容。 如无则展示“-” *   链接名称：该门店注册邀请码对应的邀请链接名称。例如“渠道A推广链接”。如无则展示“-” *   服务状态显示门店当前的服务状态：（正常运营、即将过期、服务终止） *   正常运营 *   即将到期(服务到期前X天，X可配置：3天) *   服务终止(服务已过期） *   当前奖励金：门店账户中可用于抵扣服务费用的金额。点击跳转【奖励金明细】列表 *   到期日期：门店当前付费服务或免费服务的最终到期日期。这是最重要的一个日期，整合了免费时长和付费时长后的最终到期点。 d. 操作按钮：“设置奖励金”、“设置免费时长” *   设置奖励金: 1. 点击“设置奖励金”弹窗： 点击【查看...】按钮，对应跳转“该门店”的搜索入口 范礼兴 7月29日 16:10 黄文英 7月29日 16:16 邀请人姓名(或门店名称)+手机号码 这里需要去掉或门店名称 好的 动显示当前门店的名称和Code，不可修改。 *   输入设置金额，要求输入大于0的有效金额。 *   可选充值备注。文本，不超过100字符 *   用户点击“确认”按钮。 *   更新门店的当前 奖励金 字段。生成一条新的 奖励明细 记录，记录充值时间、金额、类型（系统设置/邀请奖励）、操作人、备注等。 *   设置免费时长 1. 点击“设置免费时长”弹窗： *   设置门店：自动显示当前门店的名称和Code，不可修改。 *   输入时长天数。要求输入大于0的整数天数。叠加免费日期，设置后展示最终的到期日期 2. 可选设置备注。文本，不超过100字符 3. 确认后，更新门店的 到期日期 字段。 *   设置Ai外呼点数 *   点击【设置Ai外呼点数】 *   设置门店：自动显示当前门店的名称和Code，不可修改。 数，要求输入大于0的有效整数。 *   可选充值备注。文本，不超过100字符 *   用户点击“确认”按钮。 *   更新门店的当前可用点数字段。生成一条新的点数明细记录，记录设置时间、点数、类型（系统设置/充值）、操作人、备注等。 *   “查看详情”： 1. 点击进入该门店的门店详情页。
   门店详情页展示 页面顶部展示门店的核心识别信息：
   • 门店Code+门店名称
   • 门店房间数
   • 门店邀请码(如果有)
   • 注册邀请码
   • 邀请信息(姓名+手机号+门店code+门店名称，点击可跳转对应门店详情页)
   • 标签（标签名称+ID，点击可查看标签规则详情)，如无展示“-” 黄文英 7月22日 17:07 补充编辑--链接名称 待补充 有邀请链接，此处可进行关联，支持直接搜索关联，支持切换不同的关联链接，如规则变化，规则仅限制新邀请的门店和该门店的邀请规则，不限制历史加入的门店规则
   • 具体实例： i. 没有邀请链接，则没有邀请入口，门店仅展示“升级”入口；没有免费使用天数； ii. 有关联邀请链接，规则：7天免费使用+30天早鸟特惠+邀请奖励500元，关联后获取全部规则； iii. 更换邀请链接：规则：10天免费使用+30天早鸟特惠+邀请奖励300元，除了关联后再邀请门店奖励会变化，其他不变化；
2. 门店账户与服务状态
   醒目位置展示关键账户信息和操作按钮：
   • 当前奖励金：实时奖励金显示。
   • 操作按钮：“设置奖励金” 金”弹窗/页面
   ◦ 触发：点击门店详情页的“设置奖励金”按钮。
   • 到期日期：显示最终的服务到期日期。
   • 右上角角标展示【服务状态】：（正常运营、即将过期、服务终止）
   • 操作按钮：“设置免费时长”
   • 设置免费时长”弹窗/页面
   ◦ 触发：点击门店详情页的“设置免费时长”按钮。
   • Ai外呼点数：显示当前Ai外呼点数
   • 点击【设置Ai外呼点数】
   • “设置Ai外呼点数”弹窗/页面
   ◦ 触发：点击门店详情页的“设置Ai外呼点数”按钮。
3. 查看奖励金明细 a. 点击跳转“该门店”的【奖励明细】
4. 查看邀请记录 a. 点击跳转“该门店”的【邀请记录】
5. 查看服务时长记录 a. 点击跳转“该门店”的【服务时长记录】
6. 查看Ai外呼点数记录 a. 点击跳转“该门店”的【Ai外呼点数记录】
   邀请记录
1. 搜索筛选：
   ◦ 邀请记录ID：系统自动生成，的唯一标识符
   ◦ 邀请信息：支持通过姓名/手机号模糊搜索。
   ◦ 被邀请门店：支持通过门店Code或门店名称进行模糊搜索。
   ◦ 按时间范围：支持注册时间、门店创建时间的时间范围筛选。
   ◦ 按所属标签：下拉选择具体标签。
   ◦ 按链接名称：模糊搜索。
2. 列表字段展示：
   ◦ 邀请信息：邀请人的姓名+手机号码+门店名称code。（支持跳转门店详情页）
   ◦ 被邀请门店：被邀请门店的名称及Code。（支持跳转门店详情页）
   ◦ 注册时间：被邀请门店完成注册的时间。
   ◦ 门店创建时间：被邀请门店首次成功创建门店的时间。
   ◦ 邀请人返现金额：实际返现给邀请人的金额。返现触发条件：门店创建成功
   ◦ 被邀请人返现金额：实际返现给被邀请门店的金额。返现触发条件：门店创建成功
   ◦ 所属标签：该邀请记录关联的标签名称。按记录展示
   ◦ 邀请链接名称：该邀请记录使用的链接名称。按记录展示 点击门店，查看门店详情页
   奖励金明细
1. 展示所有门店所有账户奖励金变动记录： a. 筛选与搜索： *   按门店：支持通过门店名称或Code进行搜索 *   按类型：下拉选择【邀请奖励、服务扣费、后台赠送、奖励抵扣】类型进行筛选。 b. 列表字段： *   时间：交易发生的时间。 *   门店名称/Code：发生交易的门店。 *   类型： *   - 邀请奖励（绿色） *   - 被邀奖励（黄色） *   - 后台赠送（通过后台操作赠送）（红色） *   - 奖励抵扣（门店购买服务，奖励金可用于抵扣）（蓝色） *   奖励变动：变动的金额（收入为正，支出为负）。 *   交易前奖励：交易发生前的账户奖励金。 *   交易后奖励：交易发生后的账户奖励金。 *   备注：详细说明，如“后台赠送”、“邀请门店XXX成功”。 *   自动生成备注（系统触发） 邀请奖励：当邀请成功时，系统自动生成备注格式为"邀请门店{门店名称}({门店CODE})成功"，如"邀请门店美好酒店(ST001)成功"。 被邀奖励：当被邀请注册成功时，系统自动生成备注格式为"被{邀请人门店名称}({门店CODE})邀请注册成功奖励"，如"被总部店(ST000)邀请注册成功奖励"。 奖励抵扣：当门店购买服务使用奖励金抵扣时，系统自动生成备注格式为"购买{服务名称}，奖励金抵扣"。 *   手动输入备注（管理员操作） 后台赠送：管理员通过门店详情页面中的"设置奖励金"弹窗进行操作时，需在备注文本框中输入说明。备注为可选项，限制100字符内。输入位置为：门店详情→设置奖励金弹窗→设置备注文本框。 *   操作人/关联ID：如果是后台赠送，显示操作管理员；如果是邀请返现，显示关联的邀请记录ID（点击可查看该条邀请记录）。
   服务时长记录
1. 新增【服务时长记录】：展示所有门店的服务时长变动记录 a. 筛选与搜索 *   按门店：支持通过门店名称或Code进行搜索，筛选特定门店的余额明细。 *   按类型：下拉选择【注册奖励，后台赠送，充值获取】等交易类型进行筛选。 b. 列表字段： *   门店信息：门店Code+门店名称（可点击查看详情） *   类型：注册奖励（绿色）、后台赠送（黄色）、充值获取（红色） *   时长变化：+30天、+15天等（绿色为增加，红色为减少） *   获取后时长：变更后的总时长 *   操作人：系统自动、管理员姓名、用户自助 *   日期：记录产生时间 *   详情/备注：详细说明信息 *   自动生成详情（系统触发） 成功时，系统自动生成备注格式为"新店注册赠送{天数}天免费使用"，如"新店注册赠送30天免费使用"。 充值获取：门店充值成功时，系统自动生成备注格式为"充值{金额}元获得{天数}天服务时长"，如"充值1200元获得60天服务时长"。 *   手动输入备注（管理员操作） 后台赠送：管理员通过门店详情页面中的"设置免费时长"弹窗进行操作时，需在备注文本框中输入说明。备注为可选项，限制100字符内。 输入位置为：门店详情→设置免费时长弹窗→设置备注文本框。
3. Ai运营后台-结算管理-新增优化
   模块 核心功能 功能描述 参考图（具体请以功能描述为准）
   结算管理 交易
1. 搜索框 a. 筛选门店/门店编码 i. 支持关键词模糊-索引全称/支持模糊搜索 b. 支持订单编号搜索 i. 精确搜索 c. 支持套餐名称搜索 i. 模糊搜索 d. 点击按钮：查询 新增筛选字段 产品类型：[全部][套餐][充值包] 订单状态：[全部][待支付][已支付][已取消][已退款] 购买周期：[全部][月付][季付][年付][一次性]，一次性指【充值包】 支付方式：[全部][微信支付][支付宝][奖励抵扣]
2. 交易订单列表：字段信息如下： 交易订单列表： 动生成 b. 门店名称-门店名称+门店编号 c. 产品类型-套餐/充值包 d. 套餐信息-套餐名称/充值包名称 e. 购买产品名称-套餐内购买的具体产品名称，充值包无则“-” f. 购买周期-月度/季度/年度/一次性 x. 购买周期显示 xi. 月度：绿色显示 xii. 季度：黄色显示 xiii. 年度：红色显示 g. 订单金额-实际支付金额 h. 优惠详情-显示优惠明细 -"套餐优惠：-¥100" -"早鸟特惠：-¥500+送2月" -"奖励抵扣：-¥50" -"无优惠" i. 订单状态-待支付/已支付/已取消 i. 订单支付扭转 j. 创建时间-订单创建时间 k. 产品到期时间-服务到期日期 i. 显示产品的具体到期日期 ii. 未支付和已取消订单显示"-" l. 支付方式-微信/支付宝（本期支持微信和支付宝支付即可）“-”（空值“-”为通过奖励全额抵扣的情况）
3. 订单记录 a. 按最新时间进行排序 b. 10条记录后，支持翻页查看 c. 若无数据，则仅展示表头即可
   账单
1. 搜索导航栏 a. 筛选门店/门店编码 i. 支持关键词模糊-索引全称/支持模糊搜索 a. 搜索框 ii. 支持流水号搜索 1. 精确搜索 iii. 支持订单编号搜索 1. 精确搜索 v. 点击按钮：查询 b. 新增筛选字段： i. - 产品类型：[全部][套餐][充值包] ii. - 账单状态：[全部][待支付][已支付][已取消]，其中[已退款][部分退款]暂不需支持 iii. - 计费周期：[全部][月度][季度][年度][一次性]，一次性指【充值包】 iv. - 支付方式：[全部][微信支付][支付宝][奖励抵扣] v. - 开票状态：[全部][未开票][已开票][开票中]
2. c. 账单列表：字段信息如下： i. 流水号-支付流水标识，从易宝获取 ii. 订单编号-原始订单编号，系统自动生成 iii. 门店名称-门店名称+编号 iv. 产品类型-套餐/充值包 v. 套餐信息-套餐名称/充值包名称 vi. 购买产品名称-具体套餐关联支付的产品名称，充值包无则“-” vii. 购买周期-月度/季度/年度/一次性 viii. 到期时间-产品服务到期日期 ix. 优惠详情-显示优惠明细 -"套餐优惠：-¥100" -"早鸟特惠：-¥500+送2月" -"奖励抵扣：-¥50" -"无优惠" x. 支付金额-实际支付金额 xi. 支付方式-微信/支付宝/“-”（空值“-”为通过奖励全额抵扣的情况） xii. 交易状态-交易成功/已退款（本期不做已退款逻辑） xiii. 发票状态-[未开票][已开票][开票中][-]，全额奖励抵扣无实付金额则显示[-] xiv. 订单创建时间-下单时间 xv. 订单交易时间-支付完成时间 d. 订单记录 i. 按最新时间进行排序 ii. 10条记录后，支持翻页查看 iii. 若无数据，则仅展示表头即可
4. AI门店后台-我的账户
   模块 名称 核心需求 参考图片
   我的账户
1. 我的账户：这是门店店主的主要仪表板，提供账户关键信息的快速概览。 a. 门店信息： i. 门店Code(只读)*门店名称(只读)，门店房间数 b. 服务状态与账户概览： *   AI工具使用状态：(醒目展示，如通过颜色区分) - 正常运营 🟢 - 即将到期 🟡（例如，服务到期前3天内） - 服务终止 🔴（服务已过期） *   当前奖励金：实时奖励金显示，可用于抵扣服务费用。 *   按钮：➡“奖励明细”：点击可查看详细的收支记录。 *   按钮：余额>0元，引导“余额抵扣” *   到期日期：门店当前付费服务或免费服务的最终到期日期。 *   免费时长剩余：若有免费时长，显示剩余天数或月 我的账户概览 我的邀请
   • 展示早鸟特惠
   • 展示优惠信息（如买10个月送2个月/按年付享多少折）
   • 展示优惠倒计时
   • 引导点击按钮：➡“立即续费”：点击跳转到服务续费页面。
   • 外呼充值包：
   ◦ 可用点数：实时统计
   ◦ 引导点击按钮：➡“立即充值”：点击跳转到服务支付页面。
2. 我的邀请：这个专属部分让门店可以清晰地追踪自己发出的邀请情况。 a. 邀请管理：改成”我的邀请“ b. 邀请数据总览 i. 在列表上方，提供以下汇总数据： ii. 我的专属邀请码：醒目展示门店自己的邀请码，旁边附带： *   邀请不分成：邀请好友成功开店，您最高可得元！ *   金额按门店所属套餐等级内最高可获得的金额展示 奖励金明细 外呼充值包 ⚠注意：新增服务时长列表 等级：1499元，二级等级3999元，奖励15%，邀请门店和被邀请门店各自分成50%，则奖励金额为：3999*15%*50=300元（数值向上取整） *   ➡“复制链接”按钮 *   此处链接为带门店邀请码的注册链接 iii. 已成功邀请门店：显示累计成功邀请的门店数量。 iv. 累计获得奖励：显示通过邀请累计获得的奖励总金额。 c. 邀请记录列表 i. 交互逻辑 1. 筛选/搜索：支持按被邀请门店名称、邀请状态（所有、已成功创建、已绑定，未创建）和时间范围进行筛选。 b. 列表字段展示： *   邀请ID：系统自动生成唯一标识，6位数号码 *   被邀请门店：被邀请门店的名称及Code。（支持跳转门店详情页） *   时间： 时间：被邀请门店完成注册的时间。 *   门店创建时间：被邀请门店首次成功创建门店的时间。 *   邀请奖励 *   邀请人展示邀请人奖励，被邀请人展示被邀人奖励，只展示该门店获得的奖励 人返现金额：实际返现给邀请人的金额。返现触发条件：门店创建成功，如无则此记录不展示 *   被邀人返现金额：实际返现给被邀请门店的金额。返现触发条件：门店创建成功，如无则此记录不展示 *   标签/链接 *   所属标签：该邀请记录关联的标签名称。 链接名称：该邀请记录使用的链接名称。 *   状态： *   已成功创建 *   已绑定，未创建：指已绑定，未创建成功门店的状态
3. 奖励金明细：这个部分与运营端的余额明细相似，但仅展示当前门店自身的收支记录。 a. 交互逻辑 i. 筛选：允许按交易类型（默认显示所有，但可选择只看“邀请奖励”） ii. 列表字段：（同上） *   时间：交易发生的时间。 *   门店名称/Code：发生交易的门店。 *   类型： *   - 邀请奖励（绿色） *   - 被邀奖励（黄色） *   - 后台赠送（通过后台操作赠送）（红色） *   - 奖励抵扣（门店购买服务，奖励金可用于抵扣）（蓝色） 金额（收入为正，支出为负）。 *   交易前奖励：交易发生前的账户奖励金。 *   交易后奖励：交易发生后的账户奖励金。 *   备注：详细说明，如“后台赠送”、“邀请门店XXX成功”。 *   自动生成备注（系统触发） 邀请奖励：当邀请成功时，系统自动生成备注格式为"邀请门店{门店名称}({门店CODE})成功"，如"邀请门店美好酒店(ST001)成功"。 被邀奖励：当被邀请注册成功时，系统自动生成备注格式为"被{邀请人门店名称}({门店CODE})邀请注册成功奖励"，如"被总部店(ST000)邀请注册成功奖励"。 奖励抵扣：当门店购买服务使用奖励金抵扣时，系统自动生成备注格式为"购买{服务名称}，奖励金抵扣"，如"购买AI工具月度服务，奖励金抵扣"。 *   手动输入备注（管理员操作） 后台赠送：管理员通过门店详情页面中的"设置奖励金"弹窗进行操作时，需在备注文本框中输入说明。备注为可选项，限制100字符内。输入位置为：门店详情→设置奖励金弹窗→设置备注文本框。 *   操作人/关联ID：如果是后台赠送，显示操作管理员；如果是邀请返现，显示关联的邀请记录ID（点击可查看该条邀请记录）。 iii. 排序：默认按时间倒序排列。 iv. 视觉强调：**“邀请奖励”**类型的记录可以通过不同的颜色或图标进行强调，让店主更容易看到自己获得的福利。
4. 外呼充值包：这个部分展示外呼充值包购买的点数和交易记录 a. 筛选：允许按类型（默认显示所有）、时间范围进行筛选。
   ◦ 可用点数：统计当前可用点数
   ◦ 广告语：呼叫成功仅需1个点数
   ◦ 引导点击按钮：➡“立即充值”：点击跳转到服务支付页面。
   ◦ 列表字段
   ▪ 日期：记录产生时间（第一列，按最新时间进行更新排列）
   ▪ 类型：外呼消耗、后台赠送、注册奖励、充值获取
   ▪ 点数变化：+100点、+500点等（绿色为增加，红色为减少）
   ▪ 获取后点数：变更后的总点数（橙色显示）
   ▪ 操作人：系统自动、管理员姓名、用户自助
   ▪ 详情/备注：
   • 详情：
   ◦ 外呼消耗：外呼成功，订单号：读取消耗订单
   ◦ 充值获取：购买【外呼充值包产品名称】：读取充值订单 费赠送您【xx】点数
   ◦ 注册奖励：门店加入限时奖励，免费赠送您【xx】点数
   • 备注：后台赠送备注的内容
   ▪ 详情/备注：详细说明信息
   ▪ 自动生成详情（系统触发） 注册奖励：门店注册成功时，系统自动生成备注格式为"新店注册赠送{点数}点外呼点数"，如"新店注册赠送100点外呼点数"。 充值获取：门店充值成功时，系统自动生成备注格式为"充值{金额}元获得{点数}点外呼点数"，如"充值500元获得1000点外呼点数"。 外呼消耗：AI外呼系统每次拨打电话时，实时生成备注格式为"外呼客户{携程美团等OTA订单号}，消耗1点"，如"外呼客户在“渠道”订单号-携程/美团等OTA同步的订单号，消耗1点"。
   ▪ 手动输入备注（管理员操作） 后台赠送：管理员通过门店详情页面中的"设置AI外呼点数"弹窗进行操作时，需在备注文本框中输入说明。备注为可选项，限制100字符内。 输入位置为：门店详情→设置AI外呼点数弹窗→设置备注文本框。
   ▪ 按类型：下拉选择【注册奖励，后台赠送，充值获取】等交易类型进行筛选。 b. 列表字段：
   ▪ 日期：记录产生时间（第一列，按最新时间进行更新排列）
   ▪ 门店信息：门店Code+门店名称（可点击查看详情）
   ▪ 类型：注册奖励（绿色）、后台赠送（黄色）、充值获取（红色）
   ▪ 时长变化：+30天、+15天等（绿色为增加，红色为减少）
   ▪ 获取后时长：变更后的总时长
   ▪ 操作人：系统自动、管理员姓名、用户自助
   ▪ 详情/备注：详细说明信息
   ▪ 自动生成详情（系统触发） 注册奖励：门店注册成功时，系统自动生成备注格式为"新店注册赠送{天数}天免费使用"，如"新店注册赠送30天免费使用"。 充值获取：门店充值成功时，系统自动生成备注格式为"充值{金额}元获得{天数}天服务时长"，如"充值1200元获得60天服务时长"。
   ▪ 手动输入备注（管理员操作） 后台赠送：管理员通过门店详情页面中的"设置免费时长"弹窗进行操作时，需在备注文本框中输入说明。备注为可选项，限制100字符内。 输入位置为：门店详情→设置免费时长弹窗→设置备注文本框。
   模块 名称 核心需求 参考图片
   支付入口
1. 整体页面布局 a. 页面从上至下应依次包含以下模块： i. 页面标题：“订阅OTA智能体，解锁更多能力”。 ii. 副标题：“选择合适你的套餐,或直接邀请门店”，点击【邀请门店】跳转【邀请码】入口 iii. 订阅服务模块：用于销售按周期付费的产品。 iv. 充值包模块：用于销售按次计费的产品。 v. 支付弹窗：点击任一“购买”按钮后触发的模态对话框。
2. 【邀请门店】内容更换：（原界面） a. 文案展示更改：0元文案+奖励分成 您的OTA智能体服务将于 2025-10-17 到期 邀请门店，赚取奖励 立即邀请门店加入，轻松续费！ 邀请好友成功开店，您和好友最高可得元！ 奖励实时到账，可直接用于续费服务。 上不封顶，邀越多，赚越多！ 您的专属邀请链接： 🔗按钮：【复制链接】 成功邀请: 待入账奖励:¥****（去掉）
3. 订阅服务模块 a. 周期切换器： 提供“月”、“季”、“年”三个按钮，用于切换不同计费周期的产品价格。 “年”按钮需突出“早鸟特惠”的视觉元素: 1. 买10送2：显示"买10送2" 2. 年付折扣：显示"年付享X折" 必须包含一个动态的“优惠倒计时”，实时显示优惠剩余时间。 1. 倒计时文案：改为"买10送2特惠剩余时间"，“年付享X折特惠剩余时 14:59:39；天时分秒，从购买后开始30天倒计时 默认选中“月”
    1. 产品卡片： 根据选择的周期，动态展示所有订阅类产品。 a. 卡片内容： 产品名称：【产品名称】；如“基础套餐”、“标准套餐” 价格：醒目地显示当前周期下的总金额，并在下方以小字辅助说明折合的月度价格。 卖点：营销活动展示 1. 产品卡片活动介绍： 添加醒目的活动高亮框，带有呼吸灯动画效果 买10送2：显示"早鸟特惠：买10个月免费送2个月" 年付折扣：显示"早鸟特惠：年付享X折优惠" 套餐内容：清晰列出该套餐包含的所有套餐项目及产品内容。如果1个套餐内包含一个产品，则只展示1个产品，如果包含3个产品，则展示3个产品 购买按钮：触发支付弹窗。 月度 季度 年度
4. Ai外呼充值包 a. 告别低效的人工拨打，让AI自动完成订单确认，呼叫成功仅需1个点数 b. 卡片内容： 产品名称：产品名称 点数：重点数量突出，并在旁边标注“点” 价格：显示在下方，售价：¥300 购买按钮：【立即充值】触发支付订单弹窗。 统应弹出居中的模态对话框，包含以下功能： a. 订单摘要： 产品名称：自动填充所选的产品或套餐名。 计费周期：仅在购买订阅服务时显示（月/季/年）。 门市价显示：清楚展示产品原价 套餐优惠：门市价-售价=套餐优惠金额，此处自动计算自动计算门市价与售价的差额，如为0则不展示 b. 奖励抵扣：（充值包不展示） 奖励金显示：清晰展示当前用户的账户余额。如余额为0，则不展示，准确计算逻辑：余额不足时只抵扣实际可抵扣金额 抵扣开关：提供一个开关（Toggle），允许用户选择是否使用余额抵扣。 c. 活动优惠：（充值包不展示）此处指的是营销活动现有优惠活动产生的优惠 金额测算 i. 早鸟特惠买10月送2月，测算2个月的售价金额 *   买10月送2月：计算赠送月份的价值（如单月¥399×2月=¥798优惠） ii. 早鸟特惠年付折扣，按售价再进行打折优惠金额； *   年付折扣：计算实际折扣金额（如年付原价¥7192，9折后¥5399，优惠¥1793） d. 应付金额：根据产品价格和所有优惠自动计算出的金额。 i. 计算顺序：门市价→减去套餐优惠→减去活动优惠→减去余额抵扣=最终应付金额 奖励抵扣后展示 无推荐切换套餐 无推荐切换套餐奖励抵扣 营销活动优惠展示，奖励抵扣不能与其他优惠同享”。 充值包点击立即充值-不展示优惠金额和奖励余额 享受“早鸟特惠”的年度订阅套餐时，余额抵扣功能应关闭状态（开关可开），并给出提示文字：“奖励抵扣不能与其他优惠同享”。 iii. 金额联动：当用户开启【奖励抵扣】时，“应付金额”应实时重新计算并更新。需核算最终需支付的金额 e. 推荐切换套餐机制 i. 测算【月付最优惠】的套餐提供切换推荐选择入口展示 ii. 标题：推荐更优惠套餐 iii. 内容：选择【年付套餐】可享受【早鸟特惠】,【加赠3个月服务】 1. 内容摘要：选择【周期套餐】可享受【活动类型】，【优惠内容】 iv. 按钮：立即切换 f. 支付方式选择： 提供支付方式（如微信支付、支付宝）。 以列表形式展示，用户可单选，默认选中第一个。 g. 默认勾选：我已阅读并同意《付费套餐协议》，虚拟商品一经支付不支持退款 h. 操作按钮： 取消按钮：关闭弹窗。 立即支付按钮：进入下一步支付流程（如展示二维码或调用支付网关）。
   5.1 入口展示逻辑
   模块 名称 核心需求 参考图片
   我的账户
1. 我的账户 a. 我的账户整个界面根据【规则】进行展示： *   如从未关联标签，则无【规则】，则默认账户不展示 示账户 b. 早鸟优惠： *   若无则不展示 *   若有效期过期，则不展示 c. 我的邀请 i. 以下所有信息 ii. 若无邀请记录，则该列表不展示 iii. 若有邀请记录则展示 1. 其中：若门店禁止邀请，则“我的专属邀请码”入口隐藏 d. 余额明细 i. 以下所有信息 ii. 若无余额记录，则该列表不展示 iii. 若有余额记录则展示 e. 外呼充值包 i. 以下所有信息 ii. 若无充值记录，则该列表不展示 iii. 若有充值记录则展示 1. 新店送Ai外呼充值包，则创建门店后，默认生产一条系统充值记录
   邀请码
1. 邀请码入口 a. 去掉此处“邀请码入口”，改成“我的账户” b. 若无任何记录，则不展示
   升级
1. 【升级】为服务支付入口 a. 若无【推荐套餐】和自定义门店套餐，则不展示
6. Ai门店后台-结算管理-新增优化
   模块 核心功能 功能描述 参考图（具体请以功能描述为准）
   结算管理 交易列表 搜索框 支持订单编号搜索 精确搜索 交易订单列表： 点击按钮：查询 交易订单列表：字段重新排序和修改 订单编号-唯一标识，系统自动生成 产品类型-套餐/充值包 套餐信息-套餐名称/充值包名称 购买产品名称-套餐内购买的具体产品名称，充值包则“-” 购买周期-月度/季度/年度 购买周期显示 月度：绿色显示 季度：黄色显示 年度：红色显示 订单金额-实际支付金额 订单状态-待支付/已支付/已取消 订单支付扭转 【待支付】状态，15分钟后更新为【已取消】（讨论因为中间态处理，要改为20分钟） 优惠详情-显示优惠明细 -"套餐优惠：-¥100" -"早鸟特惠：-¥500+送2月" -"奖励抵扣：-¥50" -"无优惠" 创建时间-订单创建时间 到期时间-服务到期日期 显示产品的具体到期日期 未支付和已取消订单显示"-" 支付渠道-微信/支付宝“-”（空值“-”为通过奖励全额抵扣的情况） 订单记录 按最新时间进行排序 10条记录后，支持翻页查看 若无数据，则仅展示表头即可
   账单列表
1. 搜索框 b. 支持流水号搜索 i. 精确搜索 c. 支持订单编号搜索 i. 精确搜索 d. 支持套餐名称搜索 i. 模糊搜索 a. 流水号-支付流水标识，从易宝获取 b. 订单编号-原始订单编号，系统自动生成 c. 产品类型-套餐/充值包 d. 套餐信息-套餐名称/充值包名称 e. 购买产品名称-具体套餐关联支付的产品名称，充值包则“-” f. 购买周期-月度/季度/年度/一次性 g. 到期时间-产品服务到期日期 h. 优惠详情-显示优惠明细 -"套餐优惠：-¥100" -"早鸟特惠：-¥500+送2月" -"奖励抵扣：-¥50" -"无优惠" i. 支付金额-实际支付金额 j. 支付方式-微信/支付宝“-”（空值“-”为通过奖励全额抵扣的情况） k. 交易状态-交易成功/已退款（本期不做已退款逻辑） l. 发票状态-[未开票][已开票][开票中][-]，全额奖励抵扣无实付金额则显示[-] m. 订单创建时间-下单时间 n. 订单交易时间-支付完成时间 o. 操作-点击【开发票】/【申请发票】按钮 i. 支持勾选多个账单 1. 勾选需要开票的账单记录 2. 系统实时统计选中数量和金额 3. 退款记录自动排除，仅支持【交易成功】状态订单【申请开票】；【已开票】、【审核中】的发票不支持【申请开票】 第二步：跳转到【开具发票】页面
2. 订单记录 a. 按最新时间进行排序 b. 10条记录后，支持翻页查看 c. 若无数据，则仅展示表头即可