1.1 标签表（hds_tag）
create table hds_tag
(
id              int auto_increment comment '主键' primary key,
tag_code        varchar(64)                           not null comment '标签编码，唯一标识',
tag_name        varchar(20)                           not null comment '标签名称，唯一，最多20字符',
status          tinyint     default 0                 not null comment '状态：1-启用，0-禁用',
created_by      varchar(64) default '1'               not null comment '创建人id',
created_by_name varchar(64) default '1'               not null comment '创建人名称',
updated_by      varchar(64) default '1'               not null comment '修改人id',
updated_by_name varchar(64) default '1'               not null comment '修改人名称',
created_at      datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
updated_at      datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
row_status      int         default 1                 not null comment '记录状态(1:有效, 0:无效)',
constraint uk_tag_code
unique (tag_code),
constraint uk_tag_name
unique (tag_name),
index idx_status  (status)
)
comment '邀请标签表';
1.2 邀请码表（hds_invitation_code）
ALTER TABLE hds_invitation_code
ADD COLUMN tag_code varchar(64) DEFAULT NULL COMMENT '所属标签编码' AFTER hotel_code;

create index idx_tag_code on hds_invitation_code (tag_code);
1.3 标签规则表（hds_tag_rule）
create table hds_tag_rule
(
id              int auto_increment comment '主键' primary key,
tag_code        varchar(64)                           not null comment '标签编码',
rule_type       tinyint                               not null comment '规则类型：1-免费时长，2-AI外呼点数，3-营销活动，4-邀请返现',
is_enabled      tinyint     default 0                 not null comment '是否启用：1-启用，0-禁用',
rule_config     json                                  not null comment '规则配置JSON',
created_by      varchar(64) default '1'               not null comment '创建人id',
created_by_name varchar(64) default '1'               not null comment '创建人名称',
updated_by      varchar(64) default '1'               not null comment '修改人id',
updated_by_name varchar(64) default '1'               not null comment '修改人名称',
created_at      datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
updated_at      datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
row_status      int         default 1                 not null comment '记录状态(1:有效, 0:无效)',
constraint uk_tag_rule
unique (tag_code, rule_type)
)
comment '邀请规则表';

-- =====================================================
-- 规则配置JSON结构说明
-- =====================================================

/*
1. 免费时长规则配置 (rule_type = 1):
   {
   "duration_value": 7,        // 时长数值
   "duration_unit": 1,         // 时长单位：1-天，2-月
   "description": "新门店注册赠送7天免费使用"
   }

2. AI外呼点数规则配置 (rule_type = 2):
   {
   "points_value": 100,        // 点数数值（整数）
   "points_unit": "点数",      // 单位固定为"点数"
   "description": "新门店注册赠送100点AI外呼点数"
   }

3. 营销活动规则配置 (rule_type = 2):
   早鸟优惠(购买X送Y):
   {
   "activity_type": 1,         // 活动类型：1-购买X送Y
   "valid_days": 30,          // 优惠有效期（天）
   "buy_months": 10,          // 购买月数
   "gift_months": 2,          // 赠送月数
   "description": "30天内购买10个月服务，送2个月"
   }

早鸟优惠(年付折扣):
{
"activity_type": 2,         // 活动类型：2-年付折扣
"valid_days": 30,          // 优惠有效期（天）
"discount_rate": 85,       // 折扣比例（百分比）
"description": "年付可享85折优惠"
}

3. 邀请返现规则配置 (rule_type = 3):
   {
   "total_rate": 15,          // 返现总比例（百分比）
   "inviter_rate": 60,        // 邀请门店返现比例（百分比）
   "invitee_rate": 40,        // 被邀请门店返现比例（百分比）
   "description": "邀请成功返现15%，邀请方60%，被邀请方40%"
   }
   1.4 规则历史记录表
   create table hds_tag_rule_history
   (
   id              int auto_increment comment '主键' primary key,
   rule_id         int                                   not null comment '规则表主键ID，关联hds_tag_rule.id',
   tag_code        varchar(64)                           not null comment '标签编码',
   rule_type       tinyint                               not null comment '规则类型：1-免费时长，2-营销活动，3-邀请返现',
   rule_config     json                                  not null comment '规则配置JSON',
   effective_time  datetime    default CURRENT_TIMESTAMP not null comment '版本生效时间',
   created_by      varchar(64) default '1'               not null comment '创建人id',
   created_by_name varchar(64) default '1'               not null comment '创建人名称',
   updated_by      varchar(64) default '1'               not null comment '修改人id',
   updated_by_name varchar(64) default '1'               not null comment '修改人名称',
   created_at      datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
   updated_at      datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
   row_status      int         default 1                 not null comment '记录状态(1:有效, 0:无效)',
   key idx_rule_id on hds_rule_history (rule_id),
   key idx_tag_rule_time on hds_rule_history (tag_code, rule_type, effective_time);
   )
   comment '规则历史版本表';
   1.5 门店规则绑定表
   CREATE TABLE hds_hotel_rule_binding (
   id                  INT AUTO_INCREMENT COMMENT '主键' PRIMARY KEY,
   hotel_code          VARCHAR(64) NOT NULL COMMENT '门店编码',
   rule_type           TINYINT NOT NULL COMMENT '规则类型：1-免费时长，2-AI外呼点数，3-营销活动，4-邀请返现',
   rule_config         JSON NOT NULL COMMENT '完整的规则配置（复制自标签规则）',

   -- 追溯信息
   source_tag_code     VARCHAR(64) NOT NULL COMMENT '来源标签编码',
   source_rule_id      INT NOT NULL COMMENT '来源规则ID',
   binding_time        DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '绑定时间（门店初始化时间）',

   -- 标准审计字段
   created_by          VARCHAR(64) DEFAULT 'system' NOT NULL COMMENT '创建人ID',
   created_by_name     VARCHAR(64) DEFAULT 'system' NOT NULL COMMENT '创建人名称',
   updated_by          VARCHAR(64) DEFAULT 'system' NOT NULL COMMENT '修改人ID',
   updated_by_name     VARCHAR(64) DEFAULT 'system' NOT NULL COMMENT '修改人名称',
   created_at          DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
   updated_at          DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
   row_status          INT DEFAULT 1 NOT NULL COMMENT '记录状态(1:有效, 0:无效)',

   -- 约束和索引
   CONSTRAINT uk_hotel_rule UNIQUE (hotel_code, rule_type),
   KEY idx_hotel_code (hotel_code),
   KEY idx_rule_type (rule_type),
   KEY idx_source_tag (source_tag_code),
   KEY idx_binding_time (binding_time),
   KEY idx_created_at (created_at)
   ) COMMENT '门店规则绑定表';
   1.6 邀请记录表（hds_invite_record）
   CREATE TABLE hds_invite_record
   (
   id                    bigint auto_increment comment '主键' primary key,
   invitation_code       varchar(32)                              not null comment '邀请码',
   link_name             varchar(20)                              NOT NULL COMMENT '邀请链接名称',
   tag_code              varchar(64)                              NOT NULL COMMENT '所属标签编码',
   tag_name              varchar(20)                              NOT NULL COMMENT '所属标签名称',
   inviter_hotel_code    varchar(64)                              null comment '邀请人门店编码（运营端邀请为NULL）',
   inviter_hotel_name    varchar(100)                             null comment '邀请人门店名称',
   inviter_name          varchar(50)                              null comment '邀请人姓名',
   inviter_mobile        varchar(20)                              null comment '邀请人手机号',
   invitee_hotel_code    varchar(64)                              not null comment '被邀请门店编码',
   invitee_hotel_name    varchar(100)                             not null comment '被邀请门店名称',
   parent_id             int                                      null comment '父邀请记录ID，用于构建邀请树',
   invite_status         tinyint                                  not null default 0 comment '邀请状态：0-注册中，1-创建成功，2-已失效',
   register_time         datetime                                 not null comment '注册时间',
   success_time          datetime                                 null comment '成功创建时间',
   inviter_reward_amount decimal(10, 2) default 0.00              not null comment '邀请人返现金额',
   invitee_reward_amount decimal(10, 2) default 0.00              not null comment '被邀请人返现金额',
   created_by            varchar(64)    default '1'               not null comment '创建人id',
   created_by_name       varchar(64)    default '1'               not null comment '创建人名称',
   updated_by            varchar(64)    default '1'               not null comment '修改人id',
   updated_by_name       varchar(64)    default '1'               not null comment '修改人名称',
   created_at            datetime       default CURRENT_TIMESTAMP not null comment '创建时间',
   updated_at            datetime       default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
   row_status            int            default 1                 not null comment '记录状态(1:有效, 0:无效)',
   constraint uk_invitee_hotel
   unique (invitee_hotel_code),
   key idx_invitation_code (invitation_code),
   key idx_inviter_hotel (inviter_hotel_code),
   key idx_parent_invite (parent_id),
   key idx_register_time (register_time)
   )
   comment '邀请记录表';
   1.7 门店表
   ALTER TABLE hds_hotel_info
   ADD COLUMN ota_extend_days INT DEFAULT 0 COMMENT 'OTA扩展天数';

-- 创建索引
CREATE INDEX idx_ota_extend_days ON hds_hotel_info (ota_extend_days);
1.8 付费套餐表（hds_package）
ALTER TABLE hds_package
MODIFY COLUMN pay_mode tinyint NOT NULL COMMENT '付费方式：0-按房间数量收费，1-按门店收费，2-按房间数量等级付费';


ALTER TABLE hds_package
MODIFY COLUMN discount_mode tinyint NULL COMMENT '优惠方式：0-折扣，1-一口价';
1.9 房间等级表（hds_room_level）
create table hds_room_level
(
id              int auto_increment comment '主键' primary key,
package_code    varchar(64)                           not null comment '套餐编码',
level_no        int                                   not null comment '等级序号（1,2,3...）',
level_name      varchar(50)                           null comment '等级名称',
room_min        int                                   not null comment '房间数最小值（包含）',
room_max        int                                   not null comment '房间数最大值（包含）',
sort_order      int         default 0                 not null comment '排序',
created_by      varchar(64) default '1'               not null comment '创建人id',
created_by_name varchar(64) default '1'               not null comment '创建人名称',
updated_by      varchar(64) default '1'               not null comment '修改人id',
updated_by_name varchar(64) default '1'               not null comment '修改人名称',
created_at      datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
updated_at      datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
row_status      int         default 1                 not null comment '记录状态(1:有效, 0:无效)',
constraint uk_package_level
unique (package_code, level_no)
)
comment '套餐等级表';
1.10 产品定价表（hds_product_pricing）
-- 新增字段
ALTER TABLE hds_product_pricing
ADD COLUMN level_no int DEFAULT NULL COMMENT '等级序号，NULL表示传统定价模式（pay_mode=0,1）' AFTER product_id;

-- 修改唯一约束，支持等级
ALTER TABLE hds_product_pricing
DROP INDEX uk_product_period;

ALTER TABLE hds_product_pricing
ADD CONSTRAINT uk_product_level_period UNIQUE (product_id, level_no, period_type);

-- 添加索引
create index idx_level_no on hds_product_pricing (level_no);
1.11 门店套餐产品定价表（个性化定价：hds_hotel_product_pricing）
ALTER TABLE hds_hotel_product_pricing
ADD COLUMN level_no int DEFAULT NULL COMMENT '等级序号，NULL表示传统定价模式' AFTER product_id;


-- 修改唯一约束
ALTER TABLE hds_hotel_product_pricing
DROP INDEX uk_hotel_product_period;

ALTER TABLE hds_hotel_product_pricing
ADD CONSTRAINT uk_hotel_product_level_period UNIQUE (hotel_package_code, product_id, level_no, period_type);

create index idx_level_no_hotel on hds_hotel_product_pricing (level_no);
1.12 门店付费套餐表（hds_hotel_package）
-- 修改门店套餐表，支持等级付费模式
ALTER TABLE hds_hotel_package
MODIFY COLUMN pay_mode tinyint NOT NULL COMMENT '付费方式：0-按房间数量收费，1-按门店收费，2-按房间数量等级付费';
1.13 Ai外呼充值包（hds_ai_call_package）
create table hds_ai_call_package
(
id                    int auto_increment comment '主键' primary key,
package_name          varchar(20)                           not null comment '套餐名称，最多20字符',
price                 decimal(10, 2)                        not null comment '价格，必须大于0',
points                int                                   not null comment '点数，与AI外呼关联',
created_by            varchar(64)   default '1'             not null comment '创建人id',
created_by_name       varchar(64)   default '1'             not null comment '创建人名称',
updated_by            varchar(64)   default '1'             not null comment '修改人id',
updated_by_name       varchar(64)   default '1'             not null comment '修改人名称',
created_at            datetime      default CURRENT_TIMESTAMP not null comment '创建时间',
updated_at            datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
row_status            int           default 1               not null comment '记录状态(1:有效, 0:无效)',
constraint uk_package_name unique (package_name),
key idx_price (price),
key idx_points (points)
) comment 'AI外呼充值包表';
1.14 Ai外呼点数明细表
create table hds_ai_call_detail
(
id                    bigint auto_increment comment '主键' primary key,
hotel_code            varchar(64)                           not null comment '门店编码',
transaction_no        varchar(64)                           null comment '交易流水号，唯一',
record_type           tinyint                               not null comment '记录类型：1-注册奖励，2-后台赠送，3-充值获取，4-外呼消耗',
points_change         int                                   not null comment '点数变化（正数为增加，负数为消耗）',
points_before         int                                   not null comment '操作前点数',
points_after          int                                   not null comment '操作后点数',
related_order_no      varchar(64)                           null comment '关联OTA渠道三方订单号（外呼成功时记录）',
package_id            int                                   null comment '充值包ID（充值时记录）',
operator_id           varchar(64)                           null comment '操作人ID（后台操作时记录）',
operator_name         varchar(64)                           null comment '操作人姓名',
remark                varchar(200)                          null comment '备注说明',
operation_time        datetime      default CURRENT_TIMESTAMP not null comment '操作时间',
created_by            varchar(64)   default '1'             not null comment '创建人id',
created_by_name       varchar(64)   default '1'             not null comment '创建人名称',
updated_by            varchar(64)   default '1'             not null comment '修改人id',
updated_by_name       varchar(64)   default '1'             not null comment '修改人名称',
created_at            datetime      default CURRENT_TIMESTAMP not null comment '创建时间',
updated_at            datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
row_status            int           default 1               not null comment '记录状态(1:有效, 0:无效)',
constraint uk_transaction_no unique (transaction_no),
key idx_hotel_code (hotel_code),
key idx_record_type (record_type),
key idx_operation_time (operation_time),
key idx_related_order_no (related_order_no),
key idx_package_id (package_id)
) comment 'AI外呼点数明细表';
1.15 门店账户表
create table hds_hotel_account
(
id                  int auto_increment comment '主键' primary key,
hotel_code          varchar(64)                              not null comment '门店编码',

    -- 金额资产
    current_balance     decimal(10, 2) default 0.00              not null comment '当前账户',
    invite_reward_total decimal(10, 2) default 0.00              not null comment '累计邀请奖励',
    -- 点数资产
    ai_call_points      int            default 0                 not null comment 'AI外呼当前可用点数',

    created_by          varchar(64)    default '1'               not null comment '创建人id',
    created_by_name     varchar(64)    default '1'               not null comment '创建人名称',
    updated_by          varchar(64)    default '1'               not null comment '修改人id',
    updated_by_name     varchar(64)    default '1'               not null comment '修改人名称',
    created_at          datetime       default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at          datetime       default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    row_status          int            default 1                 not null comment '记录状态(1:有效, 0:无效)',

    constraint uk_hotel_code unique (hotel_code),
    key idx_current_balance (current_balance),
    key idx_invite_reward_total (invite_reward_total),
    key idx_ai_call_points (ai_call_points)
) comment '门店账户表';
1.16 奖励金明细表
create table hds_balance_detail
(
id              bigint auto_increment comment '主键' primary key,
hotel_code      varchar(64)                           not null comment '门店编码',
transaction_no  varchar(64)                           null comment '交易流水号，唯一',
record_type     tinyint                               not null comment '记录类型：1-邀请奖励，2-被邀奖励，3-奖励抵扣，4-后台赠送',
amount          decimal(10, 2)                        not null comment '交易金额（正数为收入，负数为支出）',
balance_before  decimal(10, 2)                        not null comment '交易前余额',
balance_after   decimal(10, 2)                        not null comment '交易后余额',
related_id      varchar(64)                           null comment '关联ID（邀请记录ID、订单ID等）',
operator_id     varchar(64)                           null comment '操作人ID（后台操作时记录）',
operator_name   varchar(64)                           null comment '操作人姓名',
remark          varchar(200)                          null comment '备注说明',
record_time     datetime    default CURRENT_TIMESTAMP not null comment '记录时间',
created_by      varchar(64) default '1'               not null comment '创建人id',
created_by_name varchar(64) default '1'               not null comment '创建人名称',
updated_by      varchar(64) default '1'               not null comment '修改人id',
updated_by_name varchar(64) default '1'               not null comment '修改人名称',
created_at      datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
updated_at      datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
row_status      int         default 1                 not null comment '记录状态(1:有效, 0:无效)',
key idx_hotel_code (hotel_code),
key idx_record_type (record_type),
key idx_record_time (record_time),
key idx_related_id (related_id),
key idx_operator_id (operator_id)
) comment '余额明细表';
1.17 服务时长明细表（hds_service_duration_detail）
CREATE TABLE hds_service_duration_detail
(
id               BIGINT AUTO_INCREMENT COMMENT '主键' PRIMARY KEY,

    -- 核心业务字段
    hotel_code       VARCHAR(64)                           NOT NULL COMMENT '门店编码',
    record_type      TINYINT                               NOT NULL COMMENT '记录类型：1-注册奖励，2-后台赠送，3-充值获取',
    operation_type   TINYINT                               NOT NULL COMMENT '操作类型：1-增加，2-减少',

    -- 时长变化信息
    duration_changed INT                                   NOT NULL COMMENT '时长变化（天数）',
    duration_before  INT                                   NOT NULL DEFAULT 0 COMMENT '变更前总时长（天数）',
    duration_after   INT                                   NOT NULL COMMENT '变更后总时长（天数）',

    -- 业务关联信息
    invitation_code  VARCHAR(32)                           NULL COMMENT '关联邀请码（注册奖励时记录）',
    recharge_amount  DECIMAL(10, 2)                        NULL COMMENT '充值金额（充值获取时记录）',
    order_id         VARCHAR(64)                           NULL COMMENT '关联订单ID（充值获取时记录）',

    -- 操作人信息
    operator_type    TINYINT                               NOT NULL COMMENT '操作人类型：1-系统自动，2-管理员操作，3-用户自助',
    operator_id      VARCHAR(64)                           NULL COMMENT '操作人ID',
    operator_name    VARCHAR(100)                          NULL COMMENT '操作人姓名',

    -- 详情备注
    remark           VARCHAR(500)                          NULL COMMENT '详情备注',

    -- 标准字段
    created_by       VARCHAR(64) DEFAULT 'system'          NOT NULL COMMENT '创建人ID',
    created_by_name  VARCHAR(64) DEFAULT 'system'          NOT NULL COMMENT '创建人名称',
    created_at       DATETIME    DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    updated_at       DATETIME    DEFAULT CURRENT_TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    row_status       INT         DEFAULT 1                 NOT NULL COMMENT '记录状态(1:有效, 0:无效)',

    -- 索引
    KEY idx_hotel_code (hotel_code),
    KEY idx_record_type (record_type),
    KEY idx_operator_type (operator_type),
    KEY idx_created_at (created_at),
    KEY idx_invitation_code (invitation_code),
    KEY idx_order_id (order_id)
) COMMENT '门店服务时长记录表';