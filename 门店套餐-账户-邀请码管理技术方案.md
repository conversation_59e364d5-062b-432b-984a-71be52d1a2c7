# 1. 项⽬概述 [1]

### 1.1 产品⽂档 [1]

*   Ai后台-邀请码&套餐管理 [1]
*   Ai后台-⻔店管理&账⼾ [1]

# 2. 系统架构 [1]

### 2.1 业务架构 [1]

# 3. Ai运营后台&Ai⻔店管理平台 [1]

### 3.1 邀请码管理 [1]

**主要改动：** [1]

*   a. 新增标签表：hds_tag [1]
*   b. 新增标签规则表：hds_tag_rule [1]
*   c. 新增标签规则历史记录表：hds_tag_rule_history [1]
*   d. 新增邀请记录表：hds_invite_record [1]
*   e. 邀请码表新增字段：tag_code [1]

规则-标签-邀请码-⻔店关系图 [1]
邀请记录关系图 [1]

#### 3.1.1 新增标签 [1]

1.  新增业务类型 [1]

    ```
    TAG(1014, "tag", "标签管理"),
    ```
2.  标签限制条件： [1]

    *   a. 标签名称：20字符以内 [1]
    *   b. 默认禁⽤ [1]

#### 3.1.2 启⽤&停⽤标签 [2]

1.  启⽤：更新 hds_tag 的 status 字段=1，同时更新 hds_invitation_code 的 status =1 [2]
2.  停⽤：更新 hds_tag 的 status 字段=0，同时更新 hds_invitation_code 的 status =0 [2]
3.  邀请码使⽤时，需判断 hds_tag 的 status 字段=1，是则⾛后续逻辑，否则直接返回，标签已停⽤ [2]

#### 3.1.3 标签删除 [2]

禁⽤状态下才可以删除，修改hds_tag的row_status=0 [2]

#### 3.1.4 规则配置 [2]

**规则类型设计：** [2]

1.  免费时⻓规则（rule_type=1） [2]

    ```
    "duration_value": 7,        // 时⻓数值   "duration_unit": 1,         // 时⻓单位：1-天，2-⽉   "description": "新⻔店注册赠送7天免费使⽤" }
    ```
2.  Ai外呼点数(rule_type=2) [2]

    ```
    {   "points_value": 100,        // 点数数值（整数）   "points_unit": "点数",      // 单位固定为"点数"   "description": "新⻔店注册赠送100点AI外呼点数" }
    ```
3.  营销活动规则（rule_type=3） [2]

    ```
    早⻦优惠(购买X送Y): {   "activity_type": 1,         // 活动类型：1-购买X送Y   "valid_days": 30,          // 优惠有效期（天）   "buy_months": 10,          // 购买⽉数   "gift_months": 2,          // 赠送⽉数   "description": "30天内购买10个⽉服务，送2个⽉" }
    ```

    ```
    早⻦优惠(年付折扣): {   "activity_type": 2,         // 活动类型：2-年付折扣   "valid_days": 30,          // 优惠有效期（天）   "discount_rate": 85,       // 折扣⽐例（百分⽐）   "description": "年付可享85折优惠" }
    ```
4.  邀请返现规则（rule_type=4） [3]

    ```
    {   "total_rate": 15,          // 返现总⽐例（百分⽐）   "inviter_rate": 60,        // 邀请⻔店返现⽐例（百分⽐）   "invitee_rate": 40,        // 被邀请⻔店返现⽐例（百分⽐）   "description": "邀请成功返现15%，邀请⽅60%，被邀请⽅40%" }
    ```

#### 3.1.5 新增邀请码 [3]

复⽤之前的 hds_invitation_code 以及相关接⼝，新增字段 [3]
**新增流程：** [3]

1.  选择标签 [3]
2.  填写链接名称（20字符） [3]
3.  根据com.wormhole.hotelds.util.CodePoolManager#getCodeFromPool⽣成邀请码code [3]

根据 hds_invite_record 表查询 [4]

### 3.2 套餐管理 [4]

#### 3.2.1 付费设置 [4]

1.  相关改动如下： [4]

    *   a. 改动⼀：新增了"按房间数量等级付费"选项 [4]
    *   b. 改动⼆：去掉付费⽅式 [4]
    *   c. 改动三：新增按房间数量等级付费 [4]
        *   i. 设计如下： [4]
            1.  新增hds_room_level表，关联付费套餐code [4]
            2.  hds_product_pricing表新增level_no [4]
            3.  hds_hotel_product_pricing新增level_no [4]
2.  修改价格计算⽅式 [4]

    *   a. 按房间数量收费 [4]
        *   i. ⻔店数量\*⽉度优惠价/季度优惠价/年度优惠价 [4]
    *   b. 按⻔店收费 [4]
        *   i. ⽉度优惠价/季度优惠价/年度优惠价 [5]
    *   c. 按房间数量等级付费 [4]
        *   i. 根据房间数量判断等级区间，然后获取对应的价格 [4]

#### 3.2.2 ⻔店套餐设置 [5]

**主要改动：** [5]

1.  修改价格计算⽅式 [5]

    *   a. 按房间数量收费 [5]
        *   i. ⻔店数量\*⽉度优惠价/季度优惠价/年度优惠价 [5]
    *   b. 按⻔店收费 [5]
        *   i. ⽉度优惠价/季度优惠价/年度优惠价 [5]
    *   c. 按房间数量等级付费 [5]
        *   i. 根据房间数量判断等级区间，然后获取对应的价格 [5]
2.  新增优惠 [5]

    *   a. 两种场景 [5]
        *   i. 早⻦优惠(购买X送Y) [5]
        *   ii. 早⻦优惠-（按年付费折扣） [5]

### 3.3 ⻔店管理 [5]

#### 3.3.1 ⻔店初始化 [5]

1.  新增渠道： [5]

    *   a. 运营后台 [5]
2.  这⾥的奖励发放： [5]

    *   a. 奖励邀请的⻔店OTA1个⽉ [5]
    *   b. 给被邀请的⻔店赠送OTA1个⽉，同时根据规则1：新⻔店免费时⻓，加送时间 [5]
    *   c. 发放Ai外呼点数 [5]
        *   i. 读取邀请码关联的标签规则配置，例如points_value=100，新增⼀条该⻔店的账⼾记录，同时设置 hds_hotel_account 表的 ai_call_points =100，同时新增⼀条Ai外呼点数流⽔记录到 hds_ai_call_detail 表 [5]

        ```
        {   "points_value": 100,        // 点数数值（整数）   "points_unit": "点数",      // 单位固定为"点数"   "description": "新⻔店注册赠送100点AI外呼点数" }
        ```
    *   d. 发放免费时⻓ [6]
        *   i. 读取邀请码关联的标签规则配置，例如duration_value=7，设置 hds_hotel_info 表的 ota_extend_months = 7，同时新增⼀条服务时⻓流⽔记录到 hds_service_duration_detail 表 [6]

        ```
        {   "duration_value": 7,        // 时⻓数值
        ```
    *   e. 奖励 [6]
        *   i. 邀请奖励计算⽅式： [6]
            1.  第⼀步：根据⻔店房间数量，判断等级区间，根据等级区间获取⽉付价格 [6]
            2.  第⼆步：计算返现⾦额，返现总⾦额=被邀请⻔店⽉付费⾦额×返现总⽐例 [6]
            3.  第三步：分配返现⾦额 [6]
                *   邀请⻔店获得=返现总⾦额×邀请⻔店返现⽐例 [6]
                *   被邀请⻔店获得=返现总⾦额×被邀请⻔店返现⽐例 [6]
        *   ii. ⽰例： [6]
            1.  场景：A⻔店邀请B⻔店 [6]
                *   B⻔店房间数：150间以上 [6]
                *   B⻔店⽉付费：3999元 [6]
                *   返现总⽐例：15% [6]
                *   邀请⻔店(A)⽐例：60% [6]
                *   被邀请⻔店(B)⽐例：40% [6]
            2.  计算过程： [6]

                ```
                1. 返现总⾦额 = 3999元 × 15% = 599.85元 → 向上取整 = 600元
                ```

                ```
                2. 分配：    - A⻔店获得 = 600元 × 60% = 360元    - B⻔店获得 = 600元 × 40% = 240元（做减法）
                ```
更新B⻔店 hds_hotel_account 的current_balance=240，同时更新A⻔店 hds_hotel_account 的current_balance的值， 新增奖励⾦明细记录到 hds_balance_detail 表 [7]

#### 3.3.2 ⻔店规则绑定和更新机制 [7]

**⽅案⼀：** [7]

*   a. 阶段⼀：⻔店初始化阶段 [7]
    *   i. ⻔店注册：⽤⼾使⽤邀请码完成⻔店注册 [7]
    *   ii. 初始化完成：⻔店完成初始化流程 [7]
    *   iii. 规则应⽤：系统根据当前最新的规则配置给予⻔店相应奖励（如免费时⻓、返现⾦额等） [7]
    *   iv. 状态记录：⻔店的 rule_refs 字段初始为空，表⽰使⽤的是初始化时的当前规则 [7]
*   b. 阶段⼆：规则更新阶段 [7]
    *   i. 更改关联的标签或者规则变更触发：运营⼈员修改某个标签的规则配置 [7]
    *   ii. 历史备份：系统⾃动将修改前的规则配置保存到 hds_tag_rule_history 表，⽣成唯⼀的历史记录ID [7]
    *   iii. 影响范围查询：系统查询该标签下关联的所有邀请码 [8]
    *   iv. ⻔店影响更新：查找所有使⽤这些邀请码初始化的⻔店 [8]
    *   v. 引⽤关系建⽴：将新创建的历史记录ID添加到这些⻔店的 rule_refs 字段中 [8]
*   c. 阶段三：规则查询阶段 [8]
    *   i. 查询触发：系统需要确定某个⻔店适⽤的规则配置 [8]
    *   ii. 状态判断：检查⻔店的 rule_refs 字段 [8]
        1.  为空：表⽰⻔店使⽤当前最新规则 [8]
        2.  不为空：表⽰⻔店受历史规则变更影响，需要使⽤特定的历史规则版本 [8]

**⽅案⼆：** [8]

1.  阶段⼀：⻔店初始化阶段 [8]

    *   a. ⻔店注册：⽤⼾使⽤邀请码完成⻔店注册 [8]
    *   b. 初始化完成：⻔店完成初始化流程 [8]
    *   c. 规则应⽤：系统根据当前最新规则给予⻔店奖励 [8]
    *   d. 绑定创建：将当前规则配置完整复制到⻔店规则绑定表中 [8]

**两种⽅案详细对⽐** [8]

**⽅案⼀：规则历史表+rule_refs字段** [8]

✅ **优点** [8]

1.  **存储效率⾼** [8]
    *   多个⻔店共享同⼀份规则快照 [8]
    *   存储空间占⽤⼩，特别是⻔店数量⼤时优势明显 [8]
    *   rule_refs 只存储ID引⽤，数据紧凑 [8]
2.  **版本管理完善** [9]
    *   完整的规则变更历史追踪 [9]
    *   ⽀持复杂的版本回溯和审计 [9]
    *   可以查看任意时间点的规则状态 [9]
3.  **扩展性强** [9]
    *   ⽀持多次规则变更的叠加影响 [9]

❌ **缺点** [9]

1.  **查询逻辑复杂** [9]
    *   需要解析 rule_refs 字段（逗号分隔的ID列表） [9]
    *   查询时需要判断使⽤历史表还是当前表 [9]
    *   多表关联查询，性能可能受影响 [9]
2.  **维护复杂度⾼** [9]
    *   规则更新时需要批量更新⻔店的 rule_refs [9]
    *   数据⼀致性维护相对复杂 [9]
    *   调试和排查问题难度较⼤ [9]
3.  **数据依赖性强** [9]
    *   ⻔店规则依赖历史表的完整性 [9]
    *   历史表数据损坏会影响⻔店规则查询（标签删除） [9]

**⽅案⼆：⻔店规则绑定表** [9]

✅ **优点** [9]

1.  **设计简洁直观** [9]
    *   ⼀对⼀的⻔店规则绑定关系 [9]
    *   查询逻辑简单，直接通过⻔店编码获取 [9]
    *   易于理解和维护 [9]
2.  **查询性能稳定** [9]
    *   单表查询，性能可预期 [9]
    *   不受规则变更次数影响 [9]
    *   ⽀持⾼并发查询场景 [9]
3.  **数据独⽴性强** [9]
    *   每个⻔店拥有独⽴的规则副本 [9]
    *   规则更新完全不影响已有⻔店 [9]
    *   数据隔离性好，避免意外影响 [9]
4.  **开发维护简单** [9]
    *   业务逻辑清晰，开发效率⾼ [9]
    *   调试和排查问题容易 [9]
    *   新⼈容易理解和上⼿ [9]

❌ **缺点** [9]

1.  **存储空间较⼤** [10]
    *   每个⻔店都存储完整的规则配置 [10]
    *   当⻔店数量庞⼤时，存储成本较⾼ [10]
    *   规则配置复杂时，冗余更明显 [10]

虽然存储空间占⽤⼤，但是实现逻辑简单，没有额外的问题 [10]

#### 3.3.3 ⻔店列表 [10]

后端⽆需改动 [10]

#### 3.3.4 Ai⼯具⻔店列表 [10]

1.  主要改动： [10]

    *   a. 新增邀请记录 hds_invite_record 表 [10]
    *   b. 新增接⼝，代码逻辑⾥添加过滤条件，过滤只包含Ai⼯具产品的⻔店 [10]

    ```
    criteria = criteria.and(HdsHotelInfoFieldEnum.ai_product_types.name())         .like("%" + AiProductTypeEnum.OTA_AGENT.getCode() + "%");
    ```
2.  字段获取规则 [10]

    *   a. ⻔店基本信息 [10]
        *   ⻔店名称+⻔店code: 直接从 hds_hotel_info 获取 hotel_name + hotel_code [10]
    *   b. 邀请相关信息 [10]
        *   ⻔店邀请码: [10]
            *   表： hds_invitation_code [11]
            *   条件： hotel_code = ⽬标⻔店编码 [11]
            *   字段： invitation_code [11]
    *   c. 邀请统计信息 [11]
        *   邀请⻔店数: [11]
            *   表： hds_invite_record [11]
            *   条件： inviter_hotel_code = ⽬标⻔店编码 [11]
            *   计算： COUNT(*) [11]
    *   d. 被邀请信息 [11]
        *   邀请⼈信息: [11]
            *   表： hds_invite_record [11]
            *   条件： invitee_hotel_code = ⽬标⻔店编码 [11]
            *   字段： inviter_name + inviter_mobile [11]
        *   标签: [11]
            *   表： hds_invite_record [11]
            *   条件： invitee_hotel_code = ⽬标⻔店编码 [11]
            *   字段： tag_name [11]
        *   链接名称: [11]
            *   表： hds_invite_record [11]
            *   条件： invitee_hotel_code = ⽬标⻔店编码 [11]
            *   字段： link_name [11]
    *   表： hds_hotel_info [11]
    *   字段： init_finished_at + ota_extend_months + ota_reward_months [12]
    *   计算：基于到期时间判定状态 [12]
    *   到期⽇期: [12]
        *   表： hds_hotel_info [12]
        *   字段： init_finished_at + ota_extend_months + ota_reward_months [12]
        *   计算： DATE_ADD(init_finished_at, INTERVAL (ota_extend_months + ota_reward_months) MONTH) [12]
    *   f. 余额 [12]
        *   i. 当前账⼾余额: [12]
            1.  表： hds_hotel_account [12]
            2.  条件： hotel_code = ⽬标⻔店编码 [12]
            3.  字段： current_balance [12]

服务状态计算 [12]

#### 3.3.5 Ai⼯具⻔店详情 [12]

1.  主要改动： [12]

    *   a. 新增接⼝ [12]
2.  字段获取规则： [12]

    *   a. 同列表接⼝ [12]
1.  主要改动： [12]

    *   a. 新增接⼝ [13]
    *   b. 更新 hds_hotel_info 表的 ota_extend_months 字段 [13]
    *   c. 新增⼀条变更记录到 hds_service_duration_record 表 [13]

#### 3.3.7 获取⻔店可购买套餐 [13]

复⽤原接⼝： `/hotel/payment/package/getAvailablePackages?hotel_code= xxx` [13]
**其它改动：** [13]
年的基础上额外返回规则⾥⾯配置的营销活动，主要流程图： [13]
活动倒计时计算： [13]
展⽰格式：天时分秒 [13]
活动结束时间 = ⻔店创建时间 + 优惠有效期天数 [13]

### 3.4 Ai外呼 [13]

#### 3.4.1 新增、编辑Ai外呼套餐 [13]

1.  主要改动： [13]

    *   a. 新增Ai外呼充值包表 hds_ai_call_package [13]
2.  主要字段： [13]

    *   a. 套餐名称（不超过20字符） [13]
    *   b. 价格（⼤于0，且是整数） [13]
    *   c. 点数（整数） [13]
3.  参数校验 [13]

    *   a. ⽤ValidatorUtils⼯具类校验参数 [13]

#### 3.4.2 Ai外呼套餐列表 [13]

1.  展⽰字段： [14]

    *   a. 展⽰套餐名称、价格、点数 [14]
    *   b. 按照点数排序 [14]

#### 3.4.3 删除Ai外呼套餐 [14]

1.  主要改动： [14]

    *   a. 修改 hds_ai_call_package 表的row_status=0 [14]

#### 3.4.4 设置Ai外呼点数 [14]

1.  主要改动： [14]

    *   a. 更新 hds_hotel_account 表的 ai_call_points 字段 [14]
    *   b. 新增⼀条Ai外呼点数明细记录到 hds_ai_call_detail 表 [14]

### 3.5 我的账⼾ [14]

#### 3.5.1 设置奖励⾦ [14]

1.  主要改动： [14]

    *   a. 更新 hds_hotel_account 表的 current_balance 字段 [14]
    *   b. 新增⼀条奖励明细记录到hds_balance_detail表 [14]

#### 3.5.2 奖励⾦明细 [14]

**类型：** [14]

1-邀请返现，2-服务扣费，3-系统设置

1.  列表查询： [14]
2.  根据 hds_balance_detail 表查询 [14]

### 3.6 ⽀付 [14]

原⻔店付费套餐 [14]

#### 3.6.1 套餐确认订单 [14]

**OTA到期时间计算和价格计算逻辑：** [14]

1.  之前的到期时间计算逻辑保持不变 [14]

    ```
    服务已过期：按当前时间开始计算，增加【计费周期】核算到期时间
    服务未过期：按到期时间开始计算，增加【计费周期】核算到期时间
    按天到期计算：如2025年5⽉10⽇ 13:50下单⽀付，购买⽉付后，到期时间为：2025年6⽉10⽇ 23:59
    ```
2.  根据规则更新 [14]

    *   a. ⻔店规则查询 [14]
        *   根据 hotel_code 查询 hds_hotel_info 表的 rule_refs 字段 [14]
        *   判断该字段是否有值（JSON格式存储规则历史ID） [15]
    *   b. 营销规则获取 [15]
        *   从 rule_refs JSON中提取规则历史ID [15]
        *   根据历史ID和 rule_type=2 （营销活动）查询hds_tag_rule_history表 [15]
    *   c. 活动类型判断 [15]
        *   解析规则配置中的 activity_type 字段 [15]
        *   activity_type=1 ：购买X送Y活动 [15]
        *   activity_type=2 ：年付折扣活动 [15]
    *   d. 有效期校验 [15]
        *   获取⻔店创建时间（ created_at ） [15]
        *   计算优惠截⽌时间：⻔店创建时间+ valid_days （优惠有效期天数） [15]
        *   判断当前时间是否超过优惠截⽌时间 [15]
        *   当前时间>优惠截⽌时间：优惠已过期，忽略该营销活动 [15]
        *   当前时间≤优惠截⽌时间：优惠仍有效，继续后续判断 [15]
    *   e. 购买条件验证（针对购买X送Y） [15]
        *   获取⽤⼾本次购买时⻓（⽉数） [15]
        *   对⽐规则配置中的 buy_months （最低购买⽉数） [15]
        *   购买时⻓<buy_months：不满⾜条件，忽略活动 [15]
        *   购买时⻓≥buy_months：满⾜条件，执⾏奖励发放 [16]
    *   f. 奖励计算与发放 [16]
        *   获取规则配置中的 gift_months （赠送⽉数） [16]
        *   在原有到期时间基础上累加 gift_months [16]
        *   更新最终到期时间 [16]

#### 3.6.2 Ai外呼充值包下单 [16]

同套餐下单 [16]

#### 3.6.3 ⽀付 [16]

同确认订单的逻辑 [16]

# 4. 数据库表结构 [16]

*   邀请码&套餐管理相关表 [16]

# 5. 接⼝⽂档 [16]

*   Ai后台-邀请码&套餐管理接⼝⽂档 [16]

# 6. 项⽬研发排期计划 [16]

| 系统 | 阶段 | 模块 | ⼦模块 | 功能 | 优先级 | pd数 | 责任⼈ |
| :------------------- | :----------- | :------------- | :----------- | :----------------------------- | :------- | :----- | :----------- |
| 集团后台&⻔店后台 | ⽅案设计 | 系统设计 | ⽆ | 系统架构设计 | p0 | 1 | 范礼兴 |
| | | | | 接⼝⽂档设计 | p0 | 2 | 范礼兴 陈永丰 |
| | 开发 | 邀请码管理 | 标签管理 | 新增、编辑、查询标签信息 | p0 | 1 | 陈永丰 |
| | | | | 标签列表查询 | p0 | 1 | |
| | | | | 启⽤、禁⽤、删除标签 | p0 | | |
| | | | | 标签规则设置 | p0 | | |
| | | | 邀请码链接管理 | 新增、编辑、查询邀请码信息 | p0 | 1 | |
| | | | | 邀请码列表查询 | p0 | 1 | |
| | | | | 启⽤、停⽤邀请码 | p0 | | |
| | | 套餐管理 | 套餐设置 | 新增、编辑、查询套餐信息 | p0 | 1 | 范礼兴 |
| | | | | 套餐列表查询 | p0 | | |
| | | | ⻔店套餐设置 | 新增、编辑、查询⻔店套餐信息 | p0 | 1 | |
| | | | | ⻔店套餐列表查询 | p0 | | |
| | | ⻔店管理 | | 新增⻔店、奖励发放 | p0 | 1 | 范礼兴 |
| | | | | ⻔店规则绑定 | p0 | | |
| | | | | Ai⼯具⻔店列表 | p0 | 2 | 范礼兴 |
| | | | | 邀请记录 | p0 | | |
| | | | | Ai⼯具⻔店详情 | p0 | 1 | |
| | | | | 获取⻔店可购买套餐 | p0 | | |
| | | | | 设置免费时⻓ | p0 | 1 | |
| | | | | 关联链接名称 | p0 | | |
| | | Ai外呼 | | 新增、编辑、删除Ai外呼套餐 | p0 | 2 | 陈永丰 |
| | | | | Ai外呼套餐列表查询 | p0 | | |
| | | | | 设置Ai外呼点数 | p0 | | |
| | | 我的账⼾ | | 设置奖励⾦ | p0 | 2 | |
| | | | | 奖励⾦明细 | p0 | | |
| | | ⽀付 | 套餐下单 | 套餐订单确认 | p0 | 1 | 范礼兴 |
| | | | 充值包 | 充值包下单、回调逻辑处理 | p0 | 2 | |
| | | | ⽀付 | 易宝⽀付 | p0 | 2 | |
| | | 订单记录 | | 账单（运营端和⻔店端） | p0 | 1 | 陈永丰 |
| **总计** | | | | | | **25** | |
| 接⼝联调 | 全局 | | | 接⼝⾃测、集成测试 | p0 | 3 | |
| 集成测试 | 全局 | | | 前后端联调 | p0 | 4 | |
| 测试 | 全局 | | | 功能测试 | p0 | 6 | |
| 上线 | 全局 | 上线部署 | | 上线准备、部署、环境验证 | p0 | 1 | |
| **总计** | | | | | | **35** | |