# Ai后台-邀请码&套餐管理

### 1. 核心功能介绍

### 1.1 操作路径：

##### 后台管理
*   A[后台管理员] --> B{创建标签与邀请链接} --> C{设置规则：设置免费时长/营销活动/返现};

##### 用户侧操作
*   E[邀请门店] --> F{分享邀请链接}--> G[被邀请门店访问链接] -- 注册成功 --> H{被邀请人创建门店};

##### 奖励判定与发放
*   H -- 门店创建成功 --> I{邀请成功判定}
*   I -- 是 --> J{发放邀请门店返现奖励} --> L[邀请门店：获得奖励通知] --> M[邀请门店：查看余额明细];
*   I -- 是 --> K{发放被邀请门店免费时长/返现/营销优惠}--> N[被邀请门店：获得奖励通知]--> O[被邀请门店：查看余额明细/服务时长]-- 服务到期前 --> P[被邀请人：引导续费通知];

### 1.2 新增菜单

*   **邀请码管理**
    *   **标签管理**
        *   用于创建和管理不同营销策略的分类标签。规则：新门店免费时长设置；2、营销活动设置：3.邀请返现设置；
*   **邀请码链接管理**
    *   用于为特定标签生成唯一的邀请链接及邀请码。
*   **套餐管理**
    *   **套餐设置**
        *   支持基础套餐设置，管理套餐规则；
    *   **门店套餐设置**
        *   支持在基础套餐设置的情况下，新增门店套餐管理同个套餐规则，不同价格设置；

### 2. 运营后台-邀请码管理

### 2.1 标签及规则设置

*   **标签管理**
    *   **创建标签**
        1.  **创建标签**：
            a. 填写标签名称（20字符以内），不支持重复名称，默认“禁用”状态。
            > 黄文英 7月18日 13:39
            > 套餐管理 套餐设置 支持基础套餐设置，…
            > 套餐管理-原付费管理和门店付费管理
            点击【启用】
        2.  **列表字段**：标签信息（含code为唯一值不变）、创建时间、免费时长、营销活动、邀请奖励
        3.  **支持标签名称/ID搜索**
        4.  **操作栏**：
            a. **启用/禁用**
            i. 该操作需二次确认
            ii. 启用则该标签下所有邀请码规则启用
            iii. 禁用则该标签下所有邀请码规则禁用且入口屏蔽
            *   *   **历史数据**：已发放奖励不受影响；已获取免费时长/营销活动/邀请返现奖励仍保持原有数据，不做变化
            *   **⚠注意**：禁用状态下，邀请码链接状态不变更，则全部邀请码处于不可用状态，新用户无法通过此码注册；提示“邀请码已失效，不支持注册使用。”
            b. **编辑**：可修改标签名称。无需考虑状态
            i. 点击编辑，默认填写已有标签名称，支持编辑
            ii. 该字段只在后台展示和变更，当命名变更时，历史产生的数据命名也同时变更；
            c. **规则**：支持标签的规则编辑和更改
            d. **删除**：支持禁用状态下标签可删除，物理删除；
*   **规则设置**
    *   **规则全览**：
        第一行展示这个规则所属的标签信息
        1.  首次编辑规则，默认是启用展开设置，支持每个功能操作【禁用】；支持编辑界面展示
            仅支持查看-置灰
            编辑-每一项规则设置支持启用禁用
            > 黄文英 7月29日 17:31
            > 邀请奖励 该字段改成“邀请奖励” @黄颖 @范礼兴
            > 黄文英 7月18日 13:45
            > 黄文英 7月18日 17:36
            > 禁用则该标签下所有邀请码规则禁用且入…
            > 禁用状态
            > 邀请码链接状态不变更
            框置灰，不可编辑；
        3.  禁用状态下方可支持编辑规则；
            a. 当禁用状态下，点击“规则”，需校验有【推荐套餐】，方可进行编辑
            b. 如未设置【推荐套餐】，则提示“请先设置【推荐套餐】再进行设置规则”
            c. 当信息填写完善，方可【启用】
            i. **⚠规则内的项目禁用，或者填写为“0”，属于已填写；**
        4.  保存设置后，规则仅限制新邀请的门店，不限制历史加入的门店
        5.  总名称改成“规则设置”
    1.  **新门店配置**
        a. **免费时长**：
        *   **配置项**：
        *   时长数值：(整数)，例如7, 30, 3。
        *   时长单位：(枚举：天,月)。
        *   为选定标签设置免费时长天数（0表示无）。
        *   **生效逻辑**：
        *   新门店注册并成功创建后，系统根据规则，自动计算并更新其账户内
        *   优先级：系统后期手动赠送，免费时长将叠加。
        *   *   例如，若已有7天，又获赠30天，等于37天，则以到期日最晚的为准。后台手动设置后免费时长直接叠加。
        b. **Ai外呼点数**
        *   **配置项**：
        *   点数数值：（整数）例如100；
        > 陈永丰 7月28日 15:35
        > 当信息填写完善，方可【启用】 @黄文英 这个问题信息填写完善是指某个标签下所有规则都有填写吗？
        > 黄文英 7月22日 16:15
        > Ai外呼点数 配置项：点数数值：（整数…
        > 此处新增需求 @汪维 @沈汝琪 @黄伟杰 @范礼兴
        费赠送点数（0表示无）
        *   **生效逻辑**
        *   新门店注册并成功创建后，系统根据规则，自动计算并更新其账户内
        *   **优先级**：
        *   系统后期手动赠送，免费点数将叠加。
        *   *   例如，若已有100点，又获赠100点，等于200点；
        **营销活动**：
    *   **通用配置**：(枚举：启用, 禁用)
    1.  选择活动类型（下拉框）：早鸟优惠
    2.  根据类型动态显示相应参数输入框：
        *   **早鸟优惠**(购买10送2)：提示“30天内购买10个月服务，送2个月”。输入优惠有效期（天），购买月数，赠送月数。
            *   **固定项**：（按年付费）
                *   购买月数(整数)：需购买的服务月数10个月
                *   赠送月数(整数)：赠送的服务月数2个月
            *   **配置项**：
                *   优惠有效期-(整数)：新门店注册后多少天内有效，例如30。
            *   **生效逻辑**：
                *   新门店通过指定邀请码注册，并在【优惠有效期】内进行首次付费购买服务，且购买时长达到【购买月数】。
                *   满足条件后，系统自动延长其服务到期日。
        *   **早鸟优惠**-（按年付费折扣）
            *   **配置项**：
                *   优惠有效期-(整数)：新门店注册后多少天内有效，例如30。
                *   年付折扣：提示“年付可享X折”。输入折扣比
                > 黄文英 7月28日 11:06
                > 早鸟优惠(购买10送2)：提示“30天内购…
                > 固定为买10送2活动
                > 黄文英 7月28日 11:07
                > 早鸟优惠(购买10送2)：提示“30天内购…
                > 固定买10送2项
            *   新门店通过指定邀请码注册，并在【优惠有效期】内进行首次付费选择按年付费。
            *   系统在结算时，自动应用此折扣。
                **活动不同享原则**：
    *   优惠活动不同享，比如早鸟和余额抵扣。若同时存在有多个活动，按价格明确优先级，优选价格低的方案，值越小优先级越高
        **邀请奖励**
    *   **通用配置**：(枚举：启用, 禁用)
    *   **邀请奖励类型**：
        *   输入返现总比例（比例0-100%）
            *   返现比例公式：（按该门店月付金额*返现总比例）：如A门店邀请B门店，则A门店属于（房间数150-∞间）月付费金额=3999元*返现总比例15%=600元（取整数，向上取整）
            *   则该返现余额充值
        *   分别设置邀请门店和被邀请门店返现比例：
            *   比例按（100%划分），邀请门店+被邀请门店比例=100%，其中一方可为0，例如：
                *   【邀请门店】返现比例：0%；
                *   【被邀请门店】邀请比例：100%
                *   **⚠注意此处名称更新为邀请门店和被邀请门店**
            *   **生效逻辑**：
                *   判定触发：当被邀请门店达到（创建门店成功）条件即可返现
                *   比如新用户通过A注册用户登陆，通过B创建门店，则返现
                > 黄文英 7月29日 17:31
                > 邀请奖励 注意更改字段 @范礼兴 @黄颖
                > 范礼兴 2小时前
                > 返现比例公式：（按该门店月付金额*返…
                > 按最低价格的产品来计算
                *   若为按比例，则按被邀请门店的月付费金额乘以比例计算。
                *   入账：计算所得金额自动充值到邀请人和被邀请人的账户余额，并记录详细的余额明细。

### 2.2 邀请链接管理

*   **邀请码链接管理**
    1.  **创建链接流程**
        a. 第一步：选择所属标签（渠道A专属、夏季特惠等）-【启用】状态
        b. 第二步：填写链接名称:20字符以内
        c. 第三步：系统自动生成随机邀请码
    2.  **列表展示**
        a. 邀请码 + 复制链接按钮（直接在邀请码旁边）
        b. 链接名称
        c. 所属标签：点击标签，展示标签详情
        d. 状态
        e. 线索门店创建量
        f. 成功门店创建量
        g. 创建时间（新增）
    3.  **操作（编辑、启用/禁用）**
        a. **编辑**
        i. ✅可修改链接名称
        b. **启用/禁用**
        i. 直接操作，需二次确认
        ii. 禁用则将关联邀请码的门店入口关闭（包括后台和Ai工具的邀请码入口）
    4.  **筛选搜索功能**
        a. 关键词搜索：支持按邀请码/链接名称搜索
        b. 标签筛选：按所属标签筛选
        > 黄颖 昨天10:29
        > 黄文英 昨天10:32
        > 所属标签：点击标签，展示标签详情 这里具体展示什么内容呢？ @黄文英
        > 标签规则的详情页
        > 【标签】增加状态展示“启用”/“禁用…
        状态筛选
    5.  **【标签】增加状态展示**
        a. “启用”/“禁用”，不同颜色的标签
        b. 当标签操作”禁用“时，页面更新为“禁用”置灰标签
        c. 当标签操作“启用”时，页面更新为“启用”置橙标签
    6.  如门店未关联邀请链接，则门店不展示邀请码

### 3. 运营后台-套餐管理

##### 新增【按房间数等级付费】产品逻辑

*   **套餐管理**
    *   **新增套餐管理**
        *   **套餐管理**：
            1.  **自定义套餐名称**：
                a. 名称为唯一值，不支持重复命名（名称不超过20个字符）
                b. 每组套餐必须先设置【付费方式】，在同一个【付费方式】的前提下新增产品，每组套餐最多不超过5个产品
            2.  **新增付费方式选项**
                a. 在原有的"按房间数量收费"和"按门店收费"基础上，新增了"按房间数量等级付费"选项
                *   **选择付费方式**：选择"按房间数量等级付费"
                *   **配置等级**：设置不同的房间数量区间
                *   **保存设置**：系统会根据酒店实际房间数自动匹配对应等级进行收费
                b. 更新了说明文字，清楚描述了三种付费方式的区别
                *   **【按房间数量收费】**：系统将自动读取酒店房间数量，最终费用 = 房间单价 × 房间数量
                *   **【按门店收费】**：系统将自动固定统一费用，不考虑房间数量，每个酒店收取固定金额
                *   **【按房间数量等级付费】**：根据酒店房间数量划分等级，不同等级按不同价格收费
            3.  **房间数量等级划分管理**
                a. 当选择"按房间数量等级付费"时，会显示专门的等级划分配置区域
                b. 包含详细的功能说明，帮助用户理解等级划分的作用和使用方法
                **说明**:
                *   **【按房间数量收费】**: 系统将自动读取酒店房间数量,最终费用=房间单价×房间数量
                *   **【按门店收费】**: 系统将自动固定统一费用,不考虑房间数量,每个酒店收取固定金额
                *   **【按房间数量等级付费】**: 根据酒店房间数量划分等级,不同等级按不同价格收费
                c. **灵活的等级配置**
                *   ✅**添加等级**：支持添加房间数量等级，最多不超过10个
                *   ✅**删除等级**：可以删除不需要的等级（保留至少第一个等级）
                *   ✅**房间区间设置**：为每个等级设置房间数量范围（如：1-50间、51-100间，保存需校验最后一个房间数量等级为9999）
                *   ✅**价格配置**：每个等级独立设置等级价格和门市价，必填
                d. **交互设计**
                *   自动编号：等级会自动编号，删除需从最后一个删除
                *   数据验证：保存时会验证等级配置的完整性和合理性
                e. **表单验证**
                *   验证房间数量区间的合理性（最少房间数 < 最多房间数）
                *   （等级内所有字段都是必填项）都已完成，防止保存不完整的配置
            4.  **产品管理**：
                a. 产品名称：同个套餐内不支持产品名称重复（名称不超过20个字符）
                b. **价格设置**
                i. 支持不同等级的月度、季度、年度价格自定义设置；必填
                ii. 支持季度价格/年度价格填写“0”，则该产品的季度/年度价格不展示；所有产品季度/年度价格为“0”，则季度/年度标签不展示
                c. 价格预览：此处删除
                d. **产品描述**：
                iv. 产品描述（支持添加产品描述展示，展示产品描述不超过10个，每个产品描述字符不超过50个字符）
                1. (1) 产品功能一
                2. (2) 产品功能二
                v. 支持增加产品套餐
                1. 支持组合产品套餐新增
            5.  **按钮**：取消｜保存
                a. 保存：支持重新编辑后保存
                i. 如果二次保存，则提示“更新将覆盖原有活动优惠内容，请确认是否保存”
                1. 按钮：取消｜保存
            1.  **其他两种【付费方式】改动**
                *   **【按房间数量收费】**：系统将自动读取酒店房间数量，最终费用 = 房间单价 × 房间数量
                *   **【按门店收费】**：系统将自动固定统一费用，不考虑房间数量，每个酒店收取固定金额
            2.  **产品管理**：
                b. **价格设置**
                i. 支持的月度、季度、年度价格自定义设置；
                填写“0”，则该产品的季度/年度价格不展示；所有产品季度/年度价格为“0”，则季度/年度标签不展示
                c. 价格预览：此处删除
*   **套餐管理**
    *   **套餐设置**
    *   **门店套餐设置**
        1.  **列表和搜索栏改动**
            a. 付费管理---套餐设置
            i. 去掉【优惠方式】栏
            b. 门店付费套餐--门店套餐设置
            i. 去掉【优惠方式】栏
        2.  其他展示逻辑不变
*   **Ai后台-协议账单管理**
*   **套餐管理**
    *   **新增门店套餐**
        1.  **门店套餐设置**：
            a. 点击【新增门店套餐】
            i. 进入【门店套餐设置】
            ii. 选择门店 1. 支持模糊输入和筛选
            iii. 选择【套餐】
            1. 同步门店信息：1. 门店名称、房间数量、计费方式
            2. 通过【套餐】关联：计费方式、不支持更改
            iv. 2. **计费方式**：
            1. 选择按房间计费，则门店费用=门市价/优惠价✖房间数量
            2. 选择按酒店计费，则门店费用=门市价/优惠价
            3. 选择等级计费，则门店费用=不同房间数等于不同等级价格
            b. 门店个性化价格设置-同步门店选择的【套餐】进行设置
            i. 留空则使用默认价格，填写则使用个性化价格，月度、季度、年度价格支持独立设置；
            ii. 当【套餐】内设置季度价格为0时，则门店的价格也默认为“0”，不支持修改
            iii. 不做价格高低校验，设置多少，则该门店展示多 按房间 按门店 按等级
            非必填
            d. **展示最终价格**：
            i. 按最终设置的价格进行表格展示
            e. **按钮**：1. 取消｜保存
            i. 点击【取消】则不需保存编辑记录，取消回退到【门店费用列表】
            ii. 点击【保存】1. 保存后跳转到【门店费用列表】
            iii. 保存后默认为【停用】状态，需要在【门店费用列表】内进行启用,
        2.  **编辑入口**：
            a. 不支持更改关联的【门店名称】
            b. 不支持更改关联的【套餐】，以及计费方式
            c. 仅支持自定义价格更改
            d. 通过【门店套餐设置列表】的【编辑】入口进入
            i. 编辑后需点击【保存】，前端方可覆盖默认价格，展示为最新按门店设置的套餐价格展示
        3.  **门店套餐价格逻辑**：
            a. 当门店没有自定义套餐价格，默认按推荐逻辑售卖套餐；
            b. 当门店自定义套餐价格，则展示自定义套餐价格；

### 3.1 等级配置：**⚠房间区间覆盖矩阵表**